# Test du Menu Contextuel

Ce fichier sert à tester le menu contextuel après les corrections.

## Test avec voca-card

```voca-card
hello | bonjour | salutation
world | monde | la planète
test | test | vérification
```

## Test avec voca-table

```voca-table
hello | bonjour | salutation
world | monde | la planète
test | test | vérification
```

## Instructions de test

1. **Clic droit** : Essayez de faire un clic droit sur les blocs ci-dessus
2. **Bouton ☰** : Si activé dans les paramètres, cliquez sur le bouton ☰
3. **Ra<PERSON><PERSON><PERSON> clavier** : Cliquez sur un bloc puis appuyez sur Shift+F10

Le menu contextuel devrait apparaître avec les options :
- Nettoyer les statistiques anciennes
- Changer de mode (carte ↔ tableau)
- Mode aléatoire/suivant (pour les cartes)
- Mode normal/inversé (pour les cartes)
