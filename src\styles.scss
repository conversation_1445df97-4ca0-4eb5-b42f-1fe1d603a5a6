@mixin voca-box-styles {
    box-shadow: 0 0.25em 0.625em var(--background-modifier-box-shadow);
    padding: var(--size-4-2);
    border: calc(var(--table-border-width)*1.5) solid var(--table-border-color);
    border-radius: var(--size-4-2);
    word-wrap: break-word;
    word-break: break-word;
}


.voca-card {
    position: relative;

    @include voca-box-styles;

    &_stat-total {
        line-height: 1;
        position: absolute;
        display: inline-block;
        left: var(--size-4-2);
        top: var(--size-4-2);
        font-size: 80%;
        color: var(--color-base-60);
    }

    &_stat {
        position: absolute;
        display: inline-block;
        right: calc(var(--size-4-5) * 1.5);
        top: var(--size-4-2);
        font-size: 80%;
        line-height: 0.8;
    }

    &_stat-delimiter {
        line-height: 1;
        color: var(--color-base-40);
        display: inline-block;
        padding: 0 3px;
    }

    &_stat-right {
        line-height: 1;
        display: inline-block;
        color: var(--background-modifier-success);
    }

    &_stat-wrong {
        line-height: 1;
        display: inline-block;
        color: var(--background-modifier-error);
    }

    &_derivative {
        display: block;
        text-align: center;
        font-size: 1.5em;
        line-height: 1;
        font-weight: 500;
    }

    &_content-container {
        margin-top: var(--size-4-2);
    }

    &_ts {
        display: block;
        line-height: 1;
        color: rgb(14, 136, 14);
        text-align: center;
    }

    &_inverted-derivative {
        display: block;
        text-align: center;
        font-size: 1.2em;
        line-height: 1.2;
        margin-top: var(--size-4-2);
    }

    &_explanation-blurred,
    &_explanation {
        font-size: 110%;
        display: flex;
        flex-direction: column;
        width: 80%;
        min-height: 70px;
        margin: 0.5em auto;
        text-align: center;
        align-items: center;
        justify-content: center;
        padding: 1em;
        background-color: rgba(47, 81, 89, 0.1);
        color: #FFF;
        transition: filter 0.3s ease;
    }

    &_explanation-blurred {
        filter: blur(8px);
        user-select: none;
        cursor: pointer;
    }

    &_buttons {
        margin-top: var(--size-4-5);
        color: #FFF;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5em;

        .voca-card_button-danger {
            background-color: var(--background-modifier-error);
        }

        .voca-card_button-success {
            background-color: var(--background-modifier-success);
        }

        @media (max-width: 300px) {
            flex-direction: column;
            gap: 1em;

            .voca-card_button {
                width: 100%;
            }
        }
    }
}

.reload-container {
    position: relative;
    width: 100%;
    display: flex;
    justify-content: flex-end;
    // align-items: center;
    margin-top: 0.5em;

    &_button-reload {
        padding: 0.5em 0.75em;
        border-radius: var(--size-4-2);
        border: 0;
        cursor: pointer;
    }

    &_play-button {
        margin-right: 0.5em;
        margin-left: 0.5em;
        padding: 0.5em 0.7em;
        border-radius: var(--size-4-2);
        border: 0;
        cursor: pointer;
        background-color: var(--interactive-accent);
        color: var(--text-on-accent);
    }

    &_context-menu-button {
        margin-right: 0.5em;
        padding: 0.5em 0.4em;
        border-radius: var(--size-4-2);
        border: 0;
        cursor: pointer;
        background-color: var(--interactive-normal);
        color: var(--text-normal);
        font-size: 1.2em;
        
        &:hover {
            background-color: var(--interactive-hover);
        }
    }

    .invert-div,
    .mode-div {
        position: absolute;
        color: peru;
        left: 0;
        margin-left: 0.5em;
        font-size: 0.7em;
        line-height: 0.7;
    }

    .invert-div {
        top: 1.1em;
        color: burlywood;
    }
}


.voca-table {
    font-size: var(--editor-font-size);
    width: 100%;

    &_derivative {
        width: 45%;
        line-height: 1.2;
    }

    &_derivative-text {
        display: inline-block;
        font-weight: bold;
    }

    &_derivative-transcription {
        display: inline-block;
        margin-left: 0.6rem;

        &-text {
            font-size: 80%;
            color: rgb(14, 136, 14);
        }

        &-delimiter {
            font-size: 80%;
            color: rgb(87, 2, 87);
        }
    }

    &_explanation {
        width: 100%;
    }
}

.voca-empty {
    width: 60%;
    text-align: center;
    letter-spacing: normal;
    color: rebeccapurple;
    margin: var(--size-4-2) 0;
}

.hidden {
    display: none;
}