name: Release Obsidian Plugin

on:
  push:
    tags:
      - "*"

jobs:
  build:
    runs-on: ubuntu-latest
    permissions:
      contents: write

    steps:
      - uses: actions/checkout@v3
      - name: Use Node.js
        uses: actions/setup-node@v3
        with:
          node-version: "20.x"

      # Setup Yarn
      - name: Setup Yarn
        run: |
          corepack enable
          yarn --version

      # Build the plugin
      - name: Build
        id: build
        run: |
          yarn install
          yarn build

      # Create the release on GitHub
      - name: Create Release
        id: create_release
        uses: ncipollo/release-action@v1.13.0
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          VERSION: ${{ github.ref }}
        with:
          allowUpdates: true
          generateReleaseNotes: true
          artifactErrorsFailBuild: false
          makeLatest: true
          artifacts: "manifest.json,main.js,styles.css"
          bodyFile: ".github/workflows/release-body.md"
