# Rapport de transition vers SCSS

Ce document explique les modifications apportées au projet pour le rendre compatible avec SCSS, en vue de l'intégrer au plugin Config. Il détaille les changements nécessaires pour transformer un plugin Obsidian standard en un plugin supportant la compilation SCSS vers CSS.

## 1. Modifications des dépendances

### Ajout de dépendances SCSS

Dans le fichier `package.json`, nous avons ajouté la dépendance suivante :

```json
"esbuild-sass-plugin": "^3.3.1"
```

Cette dépendance est essentielle pour permettre à esbuild de compiler les fichiers SCSS en CSS lors du processus de build.

## 2. Modifications de la configuration de build

### Détection des fichiers SCSS

Dans `esbuild.config.ts`, nous avons ajouté une logique pour détecter la présence de fichiers SCSS et les utiliser comme points d'entrée :

```typescript
// Check for SCSS first, then CSS in src, then in root
const scssExists = await isValidPath(srcStylesScssPath);
const stylePath = scssExists ? srcStylesScssPath :
  await isValidPath(srcStylesPath) ? srcStylesPath :
    await isValidPath(rootStylesPath) ? rootStylesPath : "";

const mainTsPath = path.join(pluginDir, "src/main.ts");
const entryPoints = stylePath ? [mainTsPath, stylePath] : [mainTsPath];
const context = await createBuildContext(buildPath, isProd, entryPoints, scssExists);
```

Cette modification permet au système de build de chercher d'abord un fichier `styles.scss` dans le dossier `src`, puis un fichier `styles.css` dans `src`, et enfin un fichier `styles.css` à la racine du projet.

### Intégration du plugin SASS

Nous avons modifié la fonction `createBuildContext` pour intégrer le plugin SASS lorsque des fichiers SCSS sont détectés :

```typescript
async function createBuildContext(buildPath: string, isProd: boolean, entryPoints: string[], hasSass: boolean): Promise<esbuild.BuildContext> {
  const plugins = [
    // Add SASS plugin if SCSS files are detected
    ...(hasSass ? [
      sassPlugin({
        syntax: 'scss',
        style: 'expanded',
      }),
      {
        name: 'remove-main-css',
        setup(build: esbuild.PluginBuild): void {
          build.onEnd(async (result) => {
            if (result.errors.length === 0) {
              await removeMainCss(buildPath);
            }
          });
        },
      }
    ] : []),
    // Autres plugins...
  ];
  // Configuration esbuild...
}
```

Le plugin SASS est configuré pour utiliser la syntaxe SCSS et un style étendu (non minifié). Nous avons également ajouté un plugin personnalisé `remove-main-css` qui supprime le fichier `main.css` généré automatiquement par esbuild lors de la compilation SCSS.

### Fonction de suppression du CSS principal

Nous avons ajouté une fonction `removeMainCss` dans `utils.ts` pour supprimer le fichier CSS principal généré par esbuild :

```typescript
/**
 * Remove main.css file generated by esbuild when compiling SCSS
 * This prevents the unwanted main.css from being included in the plugin
 */
export async function removeMainCss(outdir: string): Promise<void> {
  const mainCssPath = path.join(outdir, 'main.css');
  try {
    await rm(mainCssPath);
  } catch (error: any) {
    if (error.code !== 'ENOENT') {
      console.error(`Error removing main.css: ${error.message}`);
    }
  }
}
```

Cette fonction est nécessaire car esbuild génère un fichier `main.css` lors de la compilation SCSS, mais nous voulons uniquement le fichier `styles.css`.

## 3. Optimisation de la gestion des fichiers CSS

### Amélioration de la fonction de copie des fichiers

Nous avons optimisé la fonction `copyFilesToTargetDir` dans `utils.ts` pour gérer correctement les fichiers CSS générés à partir de SCSS :

```typescript
export async function copyFilesToTargetDir(buildPath: string): Promise<void> {
  const pluginDir = process.cwd();
  const manifestSrc = path.join(pluginDir, "manifest.json");
  const manifestDest = path.join(buildPath, "manifest.json");
  const cssDest = path.join(buildPath, "styles.css");
  const folderToRemove = path.join(buildPath, "_.._");
  
  // Check if we're developing in-place (buildPath === pluginDir)
  const isInPlace = path.resolve(buildPath) === path.resolve(pluginDir);

  try {
    await mkdir(buildPath, { recursive: true });
  } catch (error: any) {
    if (error.code !== "EEXIST") {
      console.error(`Error creating directory: ${error.message}`);
    }
  }

  // Copy manifest only if not in-place development
  if (!isInPlace) {
    try {
      await copyFile(manifestSrc, manifestDest);
    } catch (error: any) {
      console.error(`Error copying manifest: ${error.message}`);
    }
  }

  // Handle CSS files
  try {
    const buildCssPath = path.join(buildPath, "styles.css");
    const srcStylesPath = path.join(pluginDir, "src/styles.css");
    const rootStylesPath = path.join(pluginDir, "styles.css");

    // First check if CSS was generated in build directory (from SCSS compilation)
    if (await isValidPath(buildCssPath)) {
      // Always copy the generated CSS to project root for consistency
      if (!isInPlace) {
        await copyFile(buildCssPath, rootStylesPath);
      } else {
        // In-place development: copy generated CSS to root if different from build location
        if (path.resolve(buildCssPath) !== path.resolve(rootStylesPath)) {
          await copyFile(buildCssPath, rootStylesPath);
        }
      }
    }
    // Otherwise, check if CSS exists in src/styles.css
    else if (await isValidPath(srcStylesPath)) {
      if (!isInPlace) {
        await copyFile(srcStylesPath, cssDest);
      }
    }
    // Finally, check if it exists in the root
    else if (await isValidPath(rootStylesPath)) {
      if (!isInPlace) {
        await copyFile(rootStylesPath, cssDest);
      }
      if (await isValidPath(folderToRemove)) {
        await rm(folderToRemove, { recursive: true });
      }
    }
  } catch (error: any) {
    console.error(`Error copying CSS: ${error.message}`);
  }
}
```

Cette fonction a été améliorée pour :
1. Détecter si le développement se fait "in-place" (dans le dossier du plugin Obsidian) ou en externe
2. Copier le CSS généré du dossier de build vers le dossier racine du projet pour assurer la cohérence
3. Éviter les copies inutiles lorsque le développement se fait in-place

## 4. Structure SCSS utilisée

Nous avons créé un fichier `styles.scss` dans le dossier `src` qui utilise les fonctionnalités SCSS comme :

1. **Mixins** pour la réutilisation de code :
```scss
@mixin voca-box-styles {
    box-shadow: 0 0.25em 0.625em var(--background-modifier-box-shadow);
    padding: var(--size-4-2);
    border: calc(var(--table-border-width)*1.5) solid var(--table-border-color);
    border-radius: var(--size-4-2);
    word-wrap: break-word;
    word-break: break-word;
}
```

2. **Nesting** pour une meilleure organisation du code :
```scss
.voca-card {
    position: relative;

    @include voca-box-styles;

    &_stat-total {
        line-height: 1;
        position: absolute;
        display: inline-block;
        left: var(--size-4-2);
        top: var(--size-4-2);
        font-size: 80%;
        color: var(--color-base-60);
    }
    
    // Autres styles imbriqués...
}
```

3. **Media queries** imbriquées :
```scss
&_buttons {
    margin-top: var(--size-4-5);
    color: #FFF;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5em;

    .voca-card_button-danger {
        background-color: var(--background-modifier-error);
    }

    .voca-card_button-success {
        background-color: var(--background-modifier-success);
    }

    @media (max-width: 300px) {
        flex-direction: column;
        gap: 1em;

        .voca-card_button {
            width: 100%;
        }
    }
}
```

## 5. Documentation des modifications

Nous avons mis à jour le README.md pour inclure des informations sur le support SCSS :

```markdown
### Sass:
If you add a `styles.scss` file to the src folder, it will be automatically converted to CSS when running commands like npm run dev or npm run build.   
```

## 6. Résumé des modifications pour l'intégration à Plugin Config

Pour intégrer le support SCSS à Plugin Config, vous devrez :

1. **Ajouter la dépendance** `esbuild-sass-plugin` au package.json

2. **Modifier la configuration de build** pour détecter et compiler les fichiers SCSS :
   - Ajouter la détection des fichiers SCSS
   - Intégrer le plugin SASS à esbuild
   - Ajouter une fonction pour supprimer le fichier `main.css` généré

3. **Optimiser la gestion des fichiers CSS** :
   - Améliorer la fonction de copie des fichiers pour gérer correctement les fichiers CSS générés
   - Ajouter une logique pour détecter si le développement se fait in-place ou en externe

4. **Créer une structure SCSS** dans le dossier `src` qui utilise les fonctionnalités SCSS comme les mixins, le nesting et les media queries

5. **Mettre à jour la documentation** pour inclure des informations sur le support SCSS

Ces modifications permettront à Plugin Config de supporter la compilation SCSS vers CSS, offrant ainsi une meilleure expérience de développement pour les styles des plugins Obsidian.